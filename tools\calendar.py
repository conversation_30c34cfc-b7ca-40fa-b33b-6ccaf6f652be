import datetime
import uuid
import j<PERSON>
from typing import List, Dict, Optional

# In-memory mock calendar (for demo/testing)
mock_calendar = {}
appointments_db = {}  # Store appointments with unique IDs

def check_availability(date: str, time: str) -> bool:
    """
    Check if the given date and time slot is available.
    Returns True if available, False if already booked.
    """
    key = f"{date}T{time}"
    return key not in mock_calendar

def book_appointment(name: str, appointment_type: str, date: str, time: str, phone: str = "", email: str = "") -> dict:
    """
    Book an appointment in the mock calendar.
    Returns appointment details with confirmation.
    """
    key = f"{date}T{time}"
    if key in mock_calendar:
        return {"error": "Slot already booked.", "success": False}

    appointment_id = str(uuid.uuid4())[:8]  # Short ID for easy reference
    appointment_data = {
        "appointment_id": appointment_id,
        "name": name,
        "appointment_type": appointment_type,
        "date": date,
        "time": time,
        "phone": phone,
        "email": email,
        "status": "confirmed",
        "created_at": datetime.datetime.now().isoformat(),
        "booking_key": key
    }

    mock_calendar[key] = appointment_data
    appointments_db[appointment_id] = appointment_data

    return {
        "success": True,
        "appointment_id": appointment_id,
        "confirmation": f"✅ Appointment Confirmed!\n\nBooking ID: {appointment_id}\nName: {name}\nService: {appointment_type}\nDate: {date}\nTime: {time}\n\nPlease save your booking ID for future reference.",
        **appointment_data
    }

def get_appointments_by_name(name: str) -> List[Dict]:
    """
    Get all appointments for a specific person.
    """
    appointments = []
    for appointment in appointments_db.values():
        if appointment["name"].lower() == name.lower():
            appointments.append(appointment)
    return appointments

def get_appointment_by_id(appointment_id: str) -> Optional[Dict]:
    """
    Get appointment details by ID.
    """
    return appointments_db.get(appointment_id)

def cancel_appointment(appointment_id: str) -> dict:
    """
    Cancel an appointment by ID.
    """
    if appointment_id not in appointments_db:
        return {"error": "Appointment not found.", "success": False}

    appointment = appointments_db[appointment_id]
    booking_key = appointment["booking_key"]

    # Remove from both storage systems
    del appointments_db[appointment_id]
    if booking_key in mock_calendar:
        del mock_calendar[booking_key]

    return {
        "success": True,
        "message": f"✅ Appointment {appointment_id} has been cancelled successfully.",
        "cancelled_appointment": appointment
    }

def reschedule_appointment(appointment_id: str, new_date: str, new_time: str) -> dict:
    """
    Reschedule an existing appointment.
    """
    if appointment_id not in appointments_db:
        return {"error": "Appointment not found.", "success": False}

    # Check if new slot is available
    new_key = f"{new_date}T{new_time}"
    if new_key in mock_calendar:
        return {"error": "New time slot is already booked.", "success": False}

    appointment = appointments_db[appointment_id]
    old_key = appointment["booking_key"]

    # Remove from old slot
    if old_key in mock_calendar:
        del mock_calendar[old_key]

    # Update appointment details
    appointment["date"] = new_date
    appointment["time"] = new_time
    appointment["booking_key"] = new_key
    appointment["updated_at"] = datetime.datetime.now().isoformat()

    # Add to new slot
    mock_calendar[new_key] = appointment
    appointments_db[appointment_id] = appointment

    return {
        "success": True,
        "message": f"✅ Appointment {appointment_id} rescheduled successfully!",
        "updated_appointment": appointment
    }

def get_available_slots(date: str) -> List[str]:
    """
    Get available time slots for a specific date.
    """
    # Standard business hours: 9 AM to 5 PM
    business_hours = [
        "09:00", "09:30", "10:00", "10:30", "11:00", "11:30",
        "12:00", "12:30", "13:00", "13:30", "14:00", "14:30",
        "15:00", "15:30", "16:00", "16:30", "17:00"
    ]

    available_slots = []
    for time_slot in business_hours:
        key = f"{date}T{time_slot}"
        if key not in mock_calendar:
            available_slots.append(time_slot)

    return available_slots

def get_all_appointments() -> List[Dict]:
    """
    Get all appointments (for admin/debugging).
    """
    return list(appointments_db.values())

# For real Google Calendar integration, replace the above with API calls using google-api-python-client.