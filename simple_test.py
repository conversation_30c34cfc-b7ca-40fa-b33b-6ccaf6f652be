#!/usr/bin/env python3

import urllib.request
import json

def test_get_chat():
    try:
        print("Testing GET /chat...")
        response = urllib.request.urlopen("http://127.0.0.1:8000/chat")
        data = json.loads(response.read().decode())
        print(f"✅ GET /chat works!")
        print(f"Response: {data}")
        return True
    except Exception as e:
        print(f"❌ GET /chat failed: {e}")
        return False

def test_post_chat_empty():
    try:
        print("\nTesting POST /chat with empty body...")
        req = urllib.request.Request("http://127.0.0.1:8000/chat", method="POST")
        response = urllib.request.urlopen(req)
        data = json.loads(response.read().decode())
        print(f"✅ POST /chat (empty) works!")
        print(f"Response: {data}")
        return True
    except Exception as e:
        print(f"❌ POST /chat (empty) failed: {e}")
        return False

def test_post_chat_with_data():
    try:
        print("\nTesting POST /chat with JSON data...")
        test_message = "I want to book an appointment"
        data = json.dumps({"message": test_message, "session_id": "test123"}).encode()
        req = urllib.request.Request(
            "http://127.0.0.1:8000/chat",
            data=data,
            headers={"Content-Type": "application/json"},
            method="POST"
        )
        response = urllib.request.urlopen(req)
        result = json.loads(response.read().decode())
        print(f"✅ POST /chat (with data) works!")
        print(f"Response: {result}")

        # Check if the input was actually received
        if test_message in result.get("response", ""):
            print(f"✅ Input was properly received: '{test_message}'")
            return True
        else:
            print(f"❌ Input was NOT received properly")
            return False

    except Exception as e:
        print(f"❌ POST /chat (with data) failed: {e}")
        return False

def test_post_chat_different_messages():
    try:
        print("\nTesting POST /chat with different messages...")
        test_cases = [
            "Hello there!",
            "Book me an appointment for tomorrow",
            "What services do you offer?",
            "Cancel my appointment"
        ]

        success = True
        for i, message in enumerate(test_cases):
            print(f"  Test {i+1}: '{message}'")
            data = json.dumps({"message": message}).encode()
            req = urllib.request.Request(
                "http://127.0.0.1:8000/chat",
                data=data,
                headers={"Content-Type": "application/json"},
                method="POST"
            )
            response = urllib.request.urlopen(req)
            result = json.loads(response.read().decode())

            if message in result.get("response", ""):
                print(f"    ✅ Received: {message}")
            else:
                print(f"    ❌ Failed to receive: {message}")
                success = False

        return success

    except Exception as e:
        print(f"❌ Multiple message test failed: {e}")
        return False

if __name__ == "__main__":
    print("=== Testing Endpoints ===")

    success_count = 0
    total_tests = 4

    if test_get_chat():
        success_count += 1

    if test_post_chat_empty():
        success_count += 1

    if test_post_chat_with_data():
        success_count += 1

    if test_post_chat_different_messages():
        success_count += 1

    print(f"\n=== Results: {success_count}/{total_tests} tests passed ===")

    if success_count == total_tests:
        print("🎉 All endpoints are working correctly!")
        print("✅ You can call the endpoints without any input parameters")
        print("✅ Both GET and POST methods work")
        print("✅ The endpoints return proper JSON responses")
        print("✅ POST endpoint properly receives and processes input messages")
    else:
        print("⚠️  Some tests failed. Check the server logs for details.")
