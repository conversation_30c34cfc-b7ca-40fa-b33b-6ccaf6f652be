#!/usr/bin/env python3

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agent.appointment_agent import run_agent

async def test_booking():
    """Test the booking system with the user's message format"""
    
    print("🧪 Testing Booking System")
    print("=" * 50)
    
    # Test the exact message from the user
    test_message = "Type : Haircut , time : 4pm , Date 4may 2026 , Phone: +92301-6050072"
    conversation_id = "test_booking_001"
    
    print(f"📝 Test Message: {test_message}")
    print(f"🆔 Conversation ID: {conversation_id}")
    print("\n🔄 Processing...")
    
    try:
        response, session_state = await run_agent(test_message, conversation_id)
        
        print("\n✅ Response:")
        print(response)
        
        print("\n📊 Session State:")
        print(f"  - Intent: {session_state.get('intent_analysis', {}).get('primary_intent', 'unknown')}")
        print(f"  - Method: {session_state.get('method', 'unknown')}")
        print(f"  - Memory Used: {session_state.get('memory_used', False)}")
        
        # Check if booking was successful
        if "Booking ID:" in response:
            print("\n🎉 SUCCESS: Booking was processed correctly!")
            print("✅ Real booking ID was generated")
        elif "Guest User" in response:
            print("\n✅ SUCCESS: Booking processed with default name")
        else:
            print("\n⚠️  INFO: Response processed but may need additional information")
            
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_booking())
