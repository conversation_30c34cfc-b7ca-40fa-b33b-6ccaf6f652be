# AI-Powered Appointment Booking Agent

## Features
- Friendly greeting and FAQ handling
- Detects booking intent and collects appointment details
- Checks availability and books via Google Calendar (or mock)
- Confirms and thanks the user
- Multi-step agent flow using LangGraph, LangChain, and Groq LLM

## Tech Stack
- FastAPI
- LangGraph
- LangChain
- Groq API (Mixtral or LLaMA 3)
- Google Calendar API (or mock)

## Setup Instructions

1. **Clone the repository**

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   # or, if using poetry:
   poetry install
   ```

3. **Configure Environment Variables**
   - Copy `.env.example` to `.env` and fill in your API keys:
     - `GROQ_API_KEY` (for Groq LLM)
     - `GOOGLE_CLIENT_ID`, `GOOGLE_CLIENT_SECRET`, etc. (for Google Calendar)

4. **Run the FastAPI server**
   ```bash
   uvicorn main:app --reload
   ```

5. **Access the API**
   - Visit [http://localhost:8000/docs](http://localhost:8000/docs) for Swagger UI.

## Project Structure
- `main.py` — FastAPI app and API routes
- `agent/` — LangGraph agent logic
- `tools/` — Calendar integration tools
- `.env` — API keys and secrets

## Notes
- For local development or testing, a mock calendar tool is available.
- Inline comments are provided for clarity in all major files.

