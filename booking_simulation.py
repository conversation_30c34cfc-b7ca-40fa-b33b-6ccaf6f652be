#!/usr/bin/env python3
"""
Simulate the booking conversation to demonstrate the fix
"""

import re

def simulate_booking_conversation():
    print("=== SIMULATING BOOKING CONVERSATION ===\n")
    
    # Simulate the user's input from the conversation
    user_inputs = [
        "Name : <PERSON><PERSON> Abbas  , phone :+922345678 , Date: Today  , ",
        "yes this is correct",
        "men hair cut"
    ]
    
    memory_context = ""
    
    for i, user_input in enumerate(user_inputs, 1):
        print(f"--- Message {i} ---")
        print(f"User: {user_input}")
        
        # Process the input
        response = process_message(user_input, memory_context)
        print(f"Sofia: {response}")
        
        # Update memory context (simplified)
        memory_context += f"User: {user_input}\nSofia: {response}\n"
        
        print()

def process_message(user_message, memory_context=""):
    """Simplified message processing focused on booking"""
    
    # Extract booking information
    name_match = re.search(r'(?:name\s*:?\s*|name is|i\'m|call me|my name is)\s*([a-zA-Z\s]+)', user_message.lower())
    phone_match = re.search(r'phone\s*:?\s*(\+?\d+)|(\+?\d{1,4}[-.\s]?\d{3,4}[-.\s]?\d{3,4}[-.\s]?\d{3,4}|\d{3}[-.]?\d{3}[-.]?\d{4})', user_message.lower())
    phone_number = phone_match.group(1) if phone_match and phone_match.group(1) else (phone_match.group(2) if phone_match else None)
    date_match = re.search(r'(\d{4}-\d{2}-\d{2}|\d{1,2}[/-]\d{1,2}[/-]\d{2,4}|today|tomorrow|monday|tuesday|wednesday|thursday|friday|saturday|sunday)', user_message.lower())
    time_match = re.search(r'(\d{1,2}:\d{2}|\d{1,2}\s*(?:am|pm))', user_message.lower())
    
    # Service keywords
    service_keywords = {
        "haircut": "Haircut",
        "hair cut": "Haircut", 
        "hair": "Haircut",
        "men hair": "Men's Haircut",
        "men haircut": "Men's Haircut"
    }
    
    service = "General Appointment"
    for keyword, service_name in service_keywords.items():
        if keyword in user_message.lower():
            service = service_name
            break
    
    # Check what we have
    has_name = name_match or "Badar Abbas" in memory_context
    has_service = any(word in user_message.lower() for word in service_keywords.keys())
    has_date = date_match is not None or "today" in memory_context.lower()
    has_time = time_match is not None
    has_phone = phone_number is not None or "+922345678" in memory_context
    
    # Extract name from current message or memory
    name = ""
    if name_match:
        name = name_match.group(1).strip().title()
    elif "Badar Abbas" in memory_context:
        name = "Badar Abbas"
    
    # Extract phone from memory if not in current message
    phone = phone_number
    if not phone and "+922345678" in memory_context:
        phone = "+922345678"
    
    # Extract date from memory if not in current message  
    date_str = ""
    if date_match:
        date_str = date_match.group(1)
    elif "today" in memory_context.lower():
        date_str = "today"
    
    print(f"  Detected - Name: {name}, Service: {service}, Date: {date_str}, Phone: {phone}")
    print(f"  Has - Name: {has_name}, Service: {has_service}, Date: {has_date}, Time: {has_time}, Phone: {has_phone}")
    
    # Booking logic
    if has_name and has_service and has_date and has_time:
        # All info available - book appointment
        return f"Perfect! I'll book your {service} appointment for {name} on {date_str} at {time_match.group(1)}. Phone: {phone}. Your appointment is confirmed!"
    
    elif has_name and has_service and has_date and not has_time:
        # Missing only time - ask for it
        return f"Perfect! I have your details:\n\n✓ Name: {name}\n✓ Service: {service}\n✓ Date: {date_str}\n✓ Phone: {phone}\n\nWhat time would you prefer for your appointment? (e.g., 2:00 PM, 10:30 AM, etc.)"
    
    elif any([has_name, has_service, has_date, has_phone]):
        # Partial info - show what we have and what we need
        collected = []
        missing = []
        
        if has_name:
            collected.append(f"✓ Name: {name}")
        else:
            missing.append("• Your name")
            
        if has_service:
            collected.append(f"✓ Service: {service}")
        else:
            missing.append("• Type of appointment")
            
        if has_date:
            collected.append(f"✓ Date: {date_str}")
        else:
            missing.append("• Preferred date")
            
        if has_time:
            collected.append(f"✓ Time: {time_match.group(1)}")
        else:
            missing.append("• Preferred time")
            
        if has_phone:
            collected.append(f"✓ Phone: {phone}")
        else:
            missing.append("• Phone number")
        
        response = "Great! I'm collecting your booking information:\n\n"
        if collected:
            response += "\n".join(collected) + "\n\n"
        if missing:
            response += "I still need:\n" + "\n".join(missing) + "\n\n"
            response += f"Please provide the {missing[0].replace('• ', '')} to continue."
        
        return response
    
    # Handle confirmations
    elif "yes" in user_message.lower() and "correct" in user_message.lower():
        return "Great! What type of appointment would you like to book?"
    
    # Default response
    return "I'd be happy to help you book an appointment! Please provide your name, preferred date, time, and type of service you need."

if __name__ == "__main__":
    simulate_booking_conversation()
