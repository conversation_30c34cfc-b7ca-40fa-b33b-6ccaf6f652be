import requests

print("Testing POST /chat endpoint with input...")

# Test 1: Send a message
try:
    response = requests.post(
        "http://127.0.0.1:8000/chat",
        json={"message": "I want to book an appointment", "session_id": "test123"}
    )
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.json()}")
    
    # Check if our message was received
    result = response.json()
    if "I want to book an appointment" in result.get("response", ""):
        print("✅ SUCCESS: Input message was received!")
    else:
        print("❌ FAILED: Input message was NOT received!")
        
except Exception as e:
    print(f"Error: {e}")

print("\n" + "="*50)

# Test 2: Send empty body
try:
    response = requests.post("http://127.0.0.1:8000/chat")
    print(f"Empty body - Status Code: {response.status_code}")
    print(f"Empty body - Response: {response.json()}")
except Exception as e:
    print(f"Empty body error: {e}")
