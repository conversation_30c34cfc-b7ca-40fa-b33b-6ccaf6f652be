import os
import uuid
from datetime import datetime
from fastapi import FastAP<PERSON>, Request
from fastapi.responses import JSONResponse
from dotenv import load_dotenv
from pydantic import BaseModel
from typing import Optional

# Load environment variables from .env
load_dotenv()

# Import the agent logic
from agent.appointment_agent import run_agent

# Pydantic models
class ChatRequest(BaseModel):
    message: Optional[str] = "Hello"
    conversation_id: Optional[str] = None

class ConversationResponse(BaseModel):
    conversation_id: str
    created_at: str
    expires_at: str
    status: str

app = FastAPI(title="AI Appointment Booking Agent")

@app.get("/")
async def root():
    return {
        "message": "AI Appointment Booking Agent is running!",
        "endpoints": [
            "/conversation/new - Generate new conversation ID",
            "/chat - Basic chat endpoint",
            "/chat-v2 - Advanced chat with memory"
        ]
    }

@app.post("/conversation/new")
async def create_conversation():
    """
    Generate a new conversation ID for maintaining conversation context
    """
    conversation_id = str(uuid.uuid4())
    created_at = datetime.now().isoformat()

    return JSONResponse({
        "conversation_id": conversation_id,
        "created_at": created_at,
        "status": "active",
        "message": "New conversation created! Use this conversation_id in your chat requests to maintain context.",
        "usage": {
            "endpoint": "/chat-v2",
            "parameter": "conversation_id",
            "example": {
                "message": "Hello Sofia!",
                "conversation_id": conversation_id
            }
        }
    })

@app.get("/chat")
async def chat_get():
    # Direct GET call without any parameters - simple response for testing
    return JSONResponse({
        "response": "Hello! I'm your AI appointment booking assistant. How can I help you today?",
        "session_state": {"status": "ready", "method": "GET"}
    })

@app.post("/chat")
async def chat(request: Request):
    # Initialize default values
    user_message = "Hello"
    session_id = None
    received_input = False

    # Try to get data from request body using FastAPI's built-in JSON parsing
    try:
        # Use request.json() which is the proper FastAPI way
        data = await request.json()
        if data and isinstance(data, dict):
            # Successfully parsed JSON data
            user_message = data.get("message", "Hello")
            session_id = data.get("session_id")
            received_input = "message" in data and data["message"] != ""
    except Exception:
        # If no JSON body or parsing fails, try to check if there's any body at all
        try:
            body = await request.body()
            if body:
                # There was a body but JSON parsing failed
                user_message = "Invalid JSON format received"
                received_input = True
        except Exception:
            # No body at all, use defaults
            pass

    # Return response showing what was received
    return JSONResponse({
        "response": f"I received your message: '{user_message}'. I'm your AI appointment booking assistant ready to help!",
        "session_state": {
            "user_message": user_message,
            "session_id": session_id,
            "method": "POST",
            "received_input": received_input
        }
    })

# Advanced chat endpoint with memory and conversation ID
@app.post("/chat-v2")
async def chat_v2(chat_request: ChatRequest):
    """
    Advanced chat endpoint with Sofia's AI agent, memory, and conversation tracking.
    Use /conversation/new to get a conversation_id first, then use it here for context.
    """
    try:
        # Validate conversation_id
        if not chat_request.conversation_id:
            return JSONResponse({
                "error": "conversation_id is required. Please call /conversation/new first to get one.",
                "suggestion": "POST /conversation/new to generate a conversation ID",
                "example_usage": {
                    "message": "Hello Sofia!",
                    "conversation_id": "your-conversation-id-here"
                }
            }, status_code=400)

        # Call Sofia with the conversation ID
        response, conversation_state = await run_agent(chat_request.message, chat_request.conversation_id)

        return JSONResponse({
            "response": response,
            "conversation_state": conversation_state,
            "conversation_id": chat_request.conversation_id,
            "method": "Sofia-AI-with-Memory",
            "received_input": chat_request.message != "Hello",
            "timestamp": datetime.now().isoformat()
        })

    except Exception as e:
        # Fallback response if agent fails
        return JSONResponse({
            "response": f"I'm Sofia, and I'm sorry - I encountered an error: {str(e)}. But I received your message: '{chat_request.message}'. Please try again!",
            "conversation_state": {
                "user_message": chat_request.message,
                "conversation_id": chat_request.conversation_id,
                "error": str(e)
            },
            "method": "Sofia-ERROR-Fallback",
            "received_input": chat_request.message != "Hello",
            "timestamp": datetime.now().isoformat()
        }, status_code=500)

@app.get("/conversation/{conversation_id}/info")
async def get_conversation_info(conversation_id: str):
    """
    Get information about a specific conversation including memory and history
    """
    try:
        # Import memory system
        from agent.appointment_agent import conversation_memory

        # Get conversation context
        memory_context = conversation_memory.get_conversation_context(conversation_id)

        # Get short-term memory
        short_term = conversation_memory.short_term_memory.get(conversation_id, {})

        # Get long-term memory
        user_key = f"user_{conversation_id}"
        long_term = conversation_memory.long_term_memory.get(user_key, {})

        return JSONResponse({
            "conversation_id": conversation_id,
            "status": "active" if short_term else "inactive",
            "short_term_memory": {
                "message_count": len(short_term.get("messages", [])),
                "started_at": short_term.get("started_at"),
                "last_interaction": short_term.get("last_interaction"),
                "recent_messages": short_term.get("messages", [])[-3:] if short_term else []
            },
            "long_term_memory": {
                "total_conversations": long_term.get("total_conversations", 0),
                "first_met": long_term.get("first_met"),
                "last_seen": long_term.get("last_seen"),
                "user_name": long_term.get("name"),
                "preferences": long_term.get("preferences", {}),
                "relationship_notes": long_term.get("relationship_notes", [])
            },
            "memory_context_preview": memory_context[:500] + "..." if len(memory_context) > 500 else memory_context
        })

    except Exception as e:
        return JSONResponse({
            "error": f"Could not retrieve conversation info: {str(e)}",
            "conversation_id": conversation_id
        }, status_code=500)

@app.delete("/conversation/{conversation_id}")
async def delete_conversation(conversation_id: str):
    """
    Delete a conversation and its associated memory
    """
    try:
        # Import memory system
        from agent.appointment_agent import conversation_memory

        # Remove from short-term memory
        if conversation_id in conversation_memory.short_term_memory:
            del conversation_memory.short_term_memory[conversation_id]

        # Remove from long-term memory
        user_key = f"user_{conversation_id}"
        if user_key in conversation_memory.long_term_memory:
            del conversation_memory.long_term_memory[user_key]
            conversation_memory.save_long_term_memory()

        return JSONResponse({
            "message": f"Conversation {conversation_id} has been deleted successfully",
            "conversation_id": conversation_id,
            "status": "deleted"
        })

    except Exception as e:
        return JSONResponse({
            "error": f"Could not delete conversation: {str(e)}",
            "conversation_id": conversation_id
        }, status_code=500)
